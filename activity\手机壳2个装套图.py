import os
from task.base_activity import <PERSON><PERSON><PERSON><PERSON><PERSON>
from task.base_activity import <PERSON><PERSON><PERSON><PERSON>
from task.base_activity import DirectoryReader
from task.base_activity import PSFileHandler
from task.feishu_notify import send_feishu_notify
import win32com.client as win
import re

def execute_jsx(script):
    photoshop = win.Dispatch("Photoshop.Application")
    result = photoshop.DoJavaScript(script)
    return result

def get_layer_group_names(layer_set):
    names = []
    for sub_layer_set in layer_set.layerSets:
        sub_layer_name = sub_layer_set.name
        names.append(sub_layer_name)
        names.extend(get_layer_group_names(sub_layer_set))
    return names


def show_matching_and_hide_neighbors(regex):
    photoshop = win.Dispatch("Photoshop.Application")
    def show_matching_and_hide_neighbors_recursively(layers):
        for layer in layers:
            pattern = rf'{regex}\d+'
            if re.match(pattern, layer.Name):
                print(f'显示{layer.Name}')
                layer.Visible = True
                hide_sibling_layers(layer.Parent.Layers, layer.Name)
            if layer.typename == "LayerSet":
                show_matching_and_hide_neighbors_recursively(layer.Layers)
    layers = photoshop.ActiveDocument.Layers
    show_matching_and_hide_neighbors_recursively(layers)


def hide_sibling_layers(layers, current_layer_name):
    for layer in layers:
        if layer.Name != current_layer_name:
            layer.Visible = False
            print(f'隐藏{layer.Name}')


def run(base_foler, excel_file):
    # 初始化文件操作对象
    file_handle = DirectoryReader()
    # 初始化日志对象
    logger = BaseLogger()
    # 初始化psd文件处理对象
    ps_file_handle = PSFileHandler()
    # 初始化表格对象
    excel_handler = ExcelHandler(excel_file)
    
    # 打开表格
    excel_obj = excel_handler.open_excel()
    
    # 读取表格文件路径
    psd_config = excel_handler.read_sheet_data(excel_obj, "文件路径")
    
    # 检查 psd_config 是否有缺失
    field_names = ["psd文件夹", "tif文件夹", "成品文件夹"]
    missing_fields = []
    for i, field in enumerate(field_names):
        if len(psd_config) <= i or not psd_config[i][1]:
            missing_fields.append(field)
    if missing_fields:
        msg = f"以下字段在表格【文件路径】页签中不存在或为空: {', '.join(missing_fields)}"
        logger.log_error(msg)
        send_feishu_notify("存在缺失文件路径", msg)
        return

    psd_conf = {
        "psd_folder": psd_config[0][1],
        "tif_folder": psd_config[1][1],
        "result_folder": psd_config[2][1]
    }
    
    # 读取任务表格数据
    psd_data = excel_handler.read_sheet_data(excel_obj, "图案型号文件名")
    # 跳过全为空的行
    psd_data = [row for row in psd_data if any(cell not in (None, '', ' ') for cell in row)]
    
    logger.log_info(psd_conf)
    
    psd_num = len(psd_data)
    logger.log_info(f"读取数据行数: {psd_num}")
    
    # 设置结果列
    result_col_index = 0
    for index, row in enumerate(psd_data):
        if index == 0:
            if "运行结果" in [cell.value for cell in excel_obj.Sheets("图案型号文件名").Rows(1)]:
                result_col_index = [cell.value for cell in excel_obj.Sheets("图案型号文件名").Rows(1)].index("运行结果") + 1
                for row_idx in range(2, excel_obj.Sheets("图案型号文件名").UsedRange.Rows.Count + 1):
                    excel_handler.write_to_cell(excel_obj, "图案型号文件名", row_idx, result_col_index, "")
            else:
                excel_handler.write_to_cell(excel_obj, "图案型号文件名", 1, len(row) + 1, "运行结果")
                result_col_index = len(row) + 1
            continue
            
        current_num = index + 1
        logger.log_info(f"行数: {current_num}, 数据: {row}")
        
        # 创建导出文件夹
        result_folder = os.path.join(psd_conf["result_folder"], row[3] + row[4])
        if not file_handle.ensure_folder_exists(result_folder):
            logger.log_error(f"路径不存在: {result_folder}")
            excel_handler.write_to_cell(excel_obj, "图案型号文件名", current_num, result_col_index, f"失败-路径不存在: {result_folder}")
            continue
            
        # 检查PSD文件
        psd_file = os.path.join(psd_conf["psd_folder"], row[2])

        # 使用更可靠的网络路径检查方法
        # if not file_handle.check_network_path_exists(psd_file):
        #     logger.log_error(f"psd文件路径不存在: {psd_file}")
        #     excel_handler.write_to_cell(excel_obj, "图案型号文件名", current_num, result_col_index, f"失败-psd文件路径不存在: {psd_file}")
        #     continue
        if not file_handle.reliable_check_path_exists(psd_file):
            logger.log_error(f"psd文件路径不存在: {psd_file}")
            excel_handler.write_to_cell(excel_obj, "图案型号文件名", current_num, result_col_index, f"失败-psd文件路径不存在: {psd_file}")
            continue
            
        # 检查TIF文件
        tif_file1 = os.path.join(psd_conf["tif_folder"], row[0])
        tif_file2 = os.path.join(psd_conf["tif_folder"], row[1])
        
        if not os.path.exists(tif_file1):
            logger.log_error(f"tif文件路径不存在: {tif_file1}")
            excel_handler.write_to_cell(excel_obj, "图案型号文件名", current_num, result_col_index, f"失败-tif文件路径不存在: {tif_file1}")
            continue
            
        if not os.path.exists(tif_file2):
            logger.log_error(f"tif文件路径不存在: {tif_file2}")
            excel_handler.write_to_cell(excel_obj, "图案型号文件名", current_num, result_col_index, f"失败-tif文件路径不存在: {tif_file2}")
            continue
            
        try:
            # 打开PSD文件
            ps_file_handle.open_ps_file(psd_file)
            
            # 获取Photoshop应用程序对象
            ps_app = win.Dispatch("Photoshop.Application")
            
            # 替换内容
            ps_file_handle.replace_img("替换内容1", tif_file1)
            ps_file_handle.replace_img("替换内容2", tif_file2)
            
            # 导出文件
            file_list = excel_handler.read_sheet_data(excel_obj, "文件夹图层名")
            for file_item in file_list:
                export_file_path = os.path.join(result_folder, f"{row[3]}{row[4]}-{file_item[0]}.jpg")
                logger.log_info(f"导出图层={file_item[0]} ==> {export_file_path}")
                ps_file_handle.export_psd_to_jpg_with_quality(app=ps_app, export_file_path=export_file_path, layer_name=file_item[0])
                
            excel_handler.write_to_cell(excel_obj, "图案型号文件名", current_num, result_col_index, "成功")
            
            # 处理完成后关闭文件
            if index + 1 < len(psd_data) and psd_data[index+1][2] == psd_file:
                logger.log_info("文件路径相同,跳过当前操作")
                ps_file_handle.recover_init()
            else:
                logger.log_info("关闭当前文件")
                ps_file_handle.close_ps_file()
                
        except Exception as e:
            logger.log_error(f"处理行 {current_num} 时发生错误: {e}")
            excel_handler.write_to_cell(excel_obj, "图案型号文件名", current_num, result_col_index, f"失败-{e}")
            ps_file_handle.close_ps_file()
    
    excel_handler.close_excel(excel_obj)


# 添加main函数入口
# if __name__ == "__main__":
#     import sys
#     if len(sys.argv) > 2:
#         base_folder = sys.argv[1]
#         excel_file = sys.argv[2]
#     else:
#         # 默认参数
#         base_folder = "."
#         excel_file = r"手机壳2个装套图-任务模版.xlsx"
    
#     run(base_folder, excel_file)


        
