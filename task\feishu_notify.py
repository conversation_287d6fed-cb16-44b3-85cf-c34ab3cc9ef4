#coding=utf-8
"""
飞书通知模块
"""

import requests
import json
from datetime import datetime

def send_feishu_notify(title, content):
    """
    发送飞书通知
    
    参数:
        title (str): 通知标题
        content (str): 详情内容
    
    # 使用示例:
    # send_feishu_notify("美工自动化脚本", "脚本执行完成，共处理100个文件")
    # send_feishu_notify("任务完成", "所有PSD文件已处理完毕")
    # send_feishu_notify("脚本异常", "文件读取失败，请检查路径")
    """
    
    webhook_url = "https://open.feishu.cn/open-apis/bot/v2/hook/5efbdba7-9d8f-42ff-bbca-06314729912b"
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # 消息模板
    message = f"""📢 {title}
━━━━━━━━━━━━━━━━━━━━
{content}

🕐 时间: {timestamp}"""
    
    headers = {"Content-Type": "application/json"}
    data = {
        "msg_type": "text",
        "content": {
            "text": message
        }
    }
    
    try:
        response = requests.post(webhook_url, headers=headers, data=json.dumps(data))
        if response.status_code == 200:
            print("飞书通知发送成功")
        else:
            print(f"飞书通知发送失败: {response.status_code}")
    except Exception as e:
        print(f"发送飞书通知时出错: {e}")


# 测试
if __name__ == "__main__":
    send_feishu_notify("测试通知", "这是一条测试消息")
